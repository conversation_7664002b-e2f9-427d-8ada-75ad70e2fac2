import { NextResponse } from 'next/server'
import { SEOService } from '@/lib/services/seo.service'

// GET /api/seo/sitemap - Generate XML sitemap
export async function GET() {
  try {
    const sitemapEntries = await SEOService.generateSitemap()

    // Generate XML sitemap
    const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://example.com'
    
    const xmlContent = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${sitemapEntries.map(entry => `  <url>
    <loc>${siteUrl}${entry.url}</loc>
    <lastmod>${entry.lastModified.toISOString()}</lastmod>
    <changefreq>${entry.changeFrequency}</changefreq>
    <priority>${entry.priority}</priority>
  </url>`).join('\n')}
</urlset>`

    return new NextResponse(xmlContent, {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600'
      }
    })

  } catch (error) {
    console.error('Sitemap generation error:', error)
    return NextResponse.json(
      { error: 'Failed to generate sitemap' },
      { status: 500 }
    )
  }
}
