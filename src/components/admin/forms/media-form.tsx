'use client'

import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Form, FormField } from '@/components/ui/form'
import { useToast } from '@/hooks/use-toast'
import { FormFieldWrapper, FormSection, FormGrid } from './shared/form-field-wrapper'
import { UpdateMediaSchema, MediaUploadSchema, type UpdateMediaInput, type MediaUploadInput } from '@/lib/schemas'
import { 
  Upload, 
  Save, 
  X, 
  File, 
  Image as ImageIcon, 
  Video, 
  Music, 
  FileText,
  Loader2,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface MediaFormProps {
  initialData?: Partial<UpdateMediaInput>
  mode?: 'edit' | 'upload'
  onSubmit?: (data: UpdateMediaInput | MediaUploadInput) => Promise<void>
  onCancel?: () => void
  isLoading?: boolean
  currentUserId?: string
}

interface UploadFile extends File {
  id: string
  progress: number
  status: 'pending' | 'uploading' | 'success' | 'error'
  error?: string
  preview?: string
}

/**
 *  
 * @param initialData
 * @param mode
 * @param onSubmit
 * @param onCancel
 * @param isLoading
 * @param currentUserId
 */

export function MediaForm({
  initialData,
  mode = 'upload',
  onSubmit,
  onCancel,
  isLoading = false,
  currentUserId = '',
}: MediaFormProps) {
  const { toast } = useToast()
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([])
  const [isUploading, setIsUploading] = useState(false)

  const form = useForm({
    resolver: zodResolver(mode === 'edit' ? UpdateMediaSchema : MediaUploadSchema),
    defaultValues: mode === 'edit' ? {
      alt: initialData?.alt || '',
      caption: initialData?.caption || '',
      type: initialData?.type || 'OTHER',
    } : {
      files: [],
      alt: '',
      caption: '',
      folder: '',
    },
  })

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles: UploadFile[] = acceptedFiles.map((file, index) => ({
      ...file,
      id: `${Date.now()}-${index}`,
      progress: 0,
      status: 'pending',
      preview: file.type.startsWith('image/') ? URL.createObjectURL(file) : undefined,
    }))

    setUploadFiles(prev => [...prev, ...newFiles])
    form.setValue('files', [...form.getValues('files'), ...acceptedFiles])
  }, [form])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: true,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp'],
      'video/*': ['.mp4', '.webm', '.ogg'],
      'audio/*': ['.mp3', '.wav', '.ogg'],
      'application/pdf': ['.pdf'],
      'text/*': ['.txt', '.md'],
    },
    maxSize: 50 * 1024 * 1024, // 50MB
  })

  const removeFile = (fileId: string) => {
    setUploadFiles(prev => prev.filter(f => f.id !== fileId))
    const currentFiles = form.getValues('files')
    const updatedFiles = currentFiles.filter((_, index) => 
      uploadFiles.find(f => f.id === fileId) !== uploadFiles[index]
    )
    form.setValue('files', updatedFiles)
  }

  const handleSubmit = async (data: any) => {
    try {
      if (mode === 'upload' && uploadFiles.length === 0) {
        toast({
          title: 'No files selected',
          description: 'Please select at least one file to upload.',
          variant: 'destructive',
        })
        return
      }

      setIsUploading(true)

      if (mode === 'upload') {
        // Simulate upload progress
        for (let i = 0; i < uploadFiles.length; i++) {
          setUploadFiles(prev => prev.map(f => 
            f.id === uploadFiles[i].id 
              ? { ...f, status: 'uploading' }
              : f
          ))

          // Simulate progress
          for (let progress = 0; progress <= 100; progress += 10) {
            await new Promise(resolve => setTimeout(resolve, 100))
            setUploadFiles(prev => prev.map(f => 
              f.id === uploadFiles[i].id 
                ? { ...f, progress }
                : f
            ))
          }

          setUploadFiles(prev => prev.map(f => 
            f.id === uploadFiles[i].id 
              ? { ...f, status: 'success' }
              : f
          ))
        }
      }

      if (onSubmit) {
        await onSubmit(data)
        toast({
          title: 'Success!',
          description: mode === 'upload' 
            ? `${uploadFiles.length} file(s) uploaded successfully.`
            : 'Media updated successfully.',
        })
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to ${mode === 'upload' ? 'upload files' : 'update media'}. Please try again.`,
        variant: 'destructive',
      })
      
      if (mode === 'upload') {
        setUploadFiles(prev => prev.map(f => ({ 
          ...f, 
          status: 'error',
          error: 'Upload failed'
        })))
      }
    } finally {
      setIsUploading(false)
    }
  }

  const getFileIcon = (type: string) => {
    if (type.startsWith('image/')) return <ImageIcon className="h-8 w-8" />
    if (type.startsWith('video/')) return <Video className="h-8 w-8" />
    if (type.startsWith('audio/')) return <Music className="h-8 w-8" />
    if (type === 'application/pdf') return <FileText className="h-8 w-8" />
    return <File className="h-8 w-8" />
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'uploading':
        return <Loader2 className="h-4 w-4 animate-spin" />
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return null
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  if (mode === 'edit') {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ImageIcon className="h-5 w-5" />
            Edit Media
          </CardTitle>
          <CardDescription>
            Update media information and metadata.
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
              {initialData?.url && (
                <div className="space-y-2">
                  <label className="text-sm font-medium">Current Media</label>
                  <div className="border rounded-lg p-4">
                    {initialData.type === 'IMAGE' ? (
                      <img 
                        src={initialData.url} 
                        alt={initialData.alt || 'Media'} 
                        className="max-w-full h-auto max-h-48 rounded"
                      />
                    ) : (
                      <div className="flex items-center gap-3">
                        {getFileIcon(initialData.mimeType || '')}
                        <div>
                          <p className="font-medium">{initialData.originalName}</p>
                          <p className="text-sm text-muted-foreground">
                            {initialData.size && formatFileSize(initialData.size)}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              <FormSection title="Media Information">
                <FormField
                  control={form.control}
                  name="alt"
                  render={({ field }) => (
                    <FormFieldWrapper
                      label="Alt Text"
                      description="Alternative text for accessibility and SEO"
                    >
                      <Input
                        placeholder="Describe the image..."
                        {...field}
                        disabled={isLoading}
                      />
                    </FormFieldWrapper>
                  )}
                />

                <FormField
                  control={form.control}
                  name="caption"
                  render={({ field }) => (
                    <FormFieldWrapper
                      label="Caption"
                      description="Optional caption to display with the media"
                    >
                      <Textarea
                        placeholder="Add a caption..."
                        className="min-h-[80px]"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormFieldWrapper>
                  )}
                />

                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormFieldWrapper
                      label="Media Type"
                      description="Categorize this media file"
                    >
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                        disabled={isLoading}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="IMAGE">Image</SelectItem>
                          <SelectItem value="VIDEO">Video</SelectItem>
                          <SelectItem value="AUDIO">Audio</SelectItem>
                          <SelectItem value="DOCUMENT">Document</SelectItem>
                          <SelectItem value="OTHER">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormFieldWrapper>
                  )}
                />
              </FormSection>

              <div className="flex justify-end space-x-4 pt-6 border-t">
                {onCancel && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={onCancel}
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                )}
                <Button type="submit" disabled={isLoading}>
                  <Save className="h-4 w-4 mr-2" />
                  {isLoading ? 'Saving...' : 'Save Changes'}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="h-5 w-5" />
          Upload Media
        </CardTitle>
        <CardDescription>
          Upload images, videos, documents, and other media files.
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Upload Area */}
            <div
              {...getRootProps()}
              className={cn(
                'border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors',
                isDragActive ? 'border-primary bg-primary/5' : 'border-muted-foreground/25',
                'hover:border-primary/50 hover:bg-primary/5'
              )}
            >
              <input {...getInputProps()} />
              <div className="flex flex-col items-center gap-4">
                <Upload className="h-12 w-12 text-muted-foreground" />
                <div>
                  <p className="text-lg font-medium">
                    {isDragActive ? 'Drop files here' : 'Drag & drop files here'}
                  </p>
                  <p className="text-muted-foreground">
                    or click to browse files
                  </p>
                </div>
                <div className="text-sm text-muted-foreground">
                  <p>Supported formats: Images, Videos, Audio, Documents</p>
                  <p>Maximum file size: 50MB</p>
                </div>
              </div>
            </div>

            {/* File List */}
            {uploadFiles.length > 0 && (
              <div className="space-y-4">
                <h3 className="font-medium">Selected Files ({uploadFiles.length})</h3>
                <div className="space-y-3">
                  {uploadFiles.map((file) => (
                    <div key={file.id} className="border rounded-lg p-4">
                      <div className="flex items-start gap-4">
                        {file.preview ? (
                          <img 
                            src={file.preview} 
                            alt={file.name}
                            className="w-16 h-16 object-cover rounded"
                          />
                        ) : (
                          <div className="w-16 h-16 flex items-center justify-center bg-muted rounded">
                            {getFileIcon(file.type)}
                          </div>
                        )}
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="font-medium truncate">{file.name}</p>
                              <p className="text-sm text-muted-foreground">
                                {formatFileSize(file.size)} • {file.type}
                              </p>
                            </div>
                            
                            <div className="flex items-center gap-2">
                              {getStatusIcon(file.status)}
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => removeFile(file.id)}
                                disabled={isUploading}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                          
                          {file.status === 'uploading' && (
                            <div className="mt-2">
                              <Progress value={file.progress} className="h-2" />
                              <p className="text-xs text-muted-foreground mt-1">
                                {file.progress}% uploaded
                              </p>
                            </div>
                          )}
                          
                          {file.status === 'error' && file.error && (
                            <p className="text-xs text-red-500 mt-1">{file.error}</p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Metadata */}
            <FormSection title="Default Metadata">
              <FormGrid columns={2}>
                <FormField
                  control={form.control}
                  name="alt"
                  render={({ field }) => (
                    <FormFieldWrapper
                      label="Default Alt Text"
                      description="Applied to all uploaded images"
                    >
                      <Input
                        placeholder="Describe the images..."
                        {...field}
                        disabled={isLoading}
                      />
                    </FormFieldWrapper>
                  )}
                />

                <FormField
                  control={form.control}
                  name="folder"
                  render={({ field }) => (
                    <FormFieldWrapper
                      label="Folder"
                      description="Organize files in folders"
                    >
                      <Input
                        placeholder="uploads/2024"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormFieldWrapper>
                  )}
                />
              </FormGrid>

              <FormField
                control={form.control}
                name="caption"
                render={({ field }) => (
                  <FormFieldWrapper
                    label="Default Caption"
                    description="Applied to all uploaded files"
                  >
                    <Textarea
                      placeholder="Add a default caption..."
                      className="min-h-[80px]"
                      {...field}
                      disabled={isLoading}
                    />
                  </FormFieldWrapper>
                )}
              />
            </FormSection>

            <div className="flex justify-end space-x-4 pt-6 border-t">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isUploading}
                >
                  Cancel
                </Button>
              )}
              <Button 
                type="submit" 
                disabled={isUploading || uploadFiles.length === 0}
              >
                <Upload className="h-4 w-4 mr-2" />
                {isUploading ? 'Uploading...' : `Upload ${uploadFiles.length} File(s)`}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}