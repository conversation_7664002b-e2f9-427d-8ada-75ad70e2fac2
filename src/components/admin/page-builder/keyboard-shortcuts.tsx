'use client'

import { useState, useEffect } from 'react'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { 
  Keyboard, 
  Command, 
  Save, 
  Undo, 
  Redo, 
  Eye, 
  Plus,
  Copy,
  Trash2,
  ArrowUp,
  ArrowDown,
  X
} from 'lucide-react'

interface KeyboardShortcutsProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

interface Shortcut {
  keys: string[]
  description: string
  icon?: React.ReactNode
  category: string
}

const shortcuts: Shortcut[] = [
  // General
  {
    keys: ['Ctrl', 'S'],
    description: 'Save page',
    icon: <Save className="h-4 w-4" />,
    category: 'General'
  },
  {
    keys: ['Ctrl', 'Z'],
    description: 'Undo last action',
    icon: <Undo className="h-4 w-4" />,
    category: 'General'
  },
  {
    keys: ['Ctrl', 'Shift', 'Z'],
    description: 'Redo last action',
    icon: <Redo className="h-4 w-4" />,
    category: 'General'
  },
  {
    keys: ['Ctrl', 'P'],
    description: 'Toggle preview mode',
    icon: <Eye className="h-4 w-4" />,
    category: 'General'
  },
  {
    keys: ['?'],
    description: 'Show keyboard shortcuts',
    icon: <Keyboard className="h-4 w-4" />,
    category: 'General'
  },
  {
    keys: ['Escape'],
    description: 'Close dialogs/panels',
    icon: <X className="h-4 w-4" />,
    category: 'General'
  },

  // Block Operations
  {
    keys: ['Ctrl', 'D'],
    description: 'Duplicate selected block',
    icon: <Copy className="h-4 w-4" />,
    category: 'Blocks'
  },
  {
    keys: ['Delete'],
    description: 'Delete selected block',
    icon: <Trash2 className="h-4 w-4" />,
    category: 'Blocks'
  },
  {
    keys: ['Ctrl', 'ArrowUp'],
    description: 'Move block up',
    icon: <ArrowUp className="h-4 w-4" />,
    category: 'Blocks'
  },
  {
    keys: ['Ctrl', 'ArrowDown'],
    description: 'Move block down',
    icon: <ArrowDown className="h-4 w-4" />,
    category: 'Blocks'
  },
  {
    keys: ['Tab'],
    description: 'Select next block',
    category: 'Blocks'
  },
  {
    keys: ['Shift', 'Tab'],
    description: 'Select previous block',
    category: 'Blocks'
  },

  // Quick Add
  {
    keys: ['Ctrl', '1'],
    description: 'Add text block',
    icon: <Plus className="h-4 w-4" />,
    category: 'Quick Add'
  },
  {
    keys: ['Ctrl', '2'],
    description: 'Add image block',
    icon: <Plus className="h-4 w-4" />,
    category: 'Quick Add'
  },
  {
    keys: ['Ctrl', '3'],
    description: 'Add hero block',
    icon: <Plus className="h-4 w-4" />,
    category: 'Quick Add'
  },
  {
    keys: ['Ctrl', '4'],
    description: 'Add CTA block',
    icon: <Plus className="h-4 w-4" />,
    category: 'Quick Add'
  },
]

function KeyboardKey({ children }: { children: React.ReactNode }) {
  return (
    <Badge variant="outline" className="px-2 py-1 text-xs font-mono">
      {children}
    </Badge>
  )
}

function ShortcutItem({ shortcut }: { shortcut: Shortcut }) {
  const isMac = typeof navigator !== 'undefined' && navigator.platform.toUpperCase().indexOf('MAC') >= 0

  const formatKey = (key: string) => {
    if (isMac) {
      switch (key) {
        case 'Ctrl':
          return '⌘'
        case 'Alt':
          return '⌥'
        case 'Shift':
          return '⇧'
        case 'ArrowUp':
          return '↑'
        case 'ArrowDown':
          return '↓'
        case 'ArrowLeft':
          return '←'
        case 'ArrowRight':
          return '→'
        default:
          return key
      }
    }
    return key
  }

  return (
    <div className="flex items-center justify-between py-2">
      <div className="flex items-center space-x-3">
        {shortcut.icon && (
          <div className="text-muted-foreground">
            {shortcut.icon}
          </div>
        )}
        <span className="text-sm">{shortcut.description}</span>
      </div>
      <div className="flex items-center space-x-1">
        {shortcut.keys.map((key, index) => (
          <div key={index} className="flex items-center space-x-1">
            <KeyboardKey>{formatKey(key)}</KeyboardKey>
            {index < shortcut.keys.length - 1 && (
              <span className="text-xs text-muted-foreground">+</span>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}

export function KeyboardShortcuts({ open, onOpenChange }: KeyboardShortcutsProps) {
  const [isMac, setIsMac] = useState(false)

  useEffect(() => {
    setIsMac(navigator.platform.toUpperCase().indexOf('MAC') >= 0)
  }, [])

  const groupedShortcuts = shortcuts.reduce((acc, shortcut) => {
    if (!acc[shortcut.category]) {
      acc[shortcut.category] = []
    }
    acc[shortcut.category].push(shortcut)
    return acc
  }, {} as Record<string, Shortcut[]>)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Keyboard className="h-5 w-5" />
            <span>Keyboard Shortcuts</span>
          </DialogTitle>
          <DialogDescription>
            Speed up your workflow with these keyboard shortcuts
          </DialogDescription>
        </DialogHeader>

        <div className="overflow-y-auto space-y-6">
          {Object.entries(groupedShortcuts).map(([category, categoryShortcuts]) => (
            <Card key={category}>
              <CardHeader className="pb-3">
                <CardTitle className="text-base">{category}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-1">
                {categoryShortcuts.map((shortcut, index) => (
                  <div key={index}>
                    <ShortcutItem shortcut={shortcut} />
                    {index < categoryShortcuts.length - 1 && (
                      <Separator className="my-2" />
                    )}
                  </div>
                ))}
              </CardContent>
            </Card>
          ))}

          <Card>
            <CardHeader>
              <CardTitle className="text-base">Tips</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 text-sm text-muted-foreground">
              <p>• Hold Shift while dragging to constrain movement</p>
              <p>• Use arrow keys to fine-tune block positioning</p>
              <p>• Double-click a block to quickly edit its content</p>
              <p>• Press Escape to deselect all blocks</p>
              {isMac ? (
                <p>• Use ⌘ instead of Ctrl on Mac</p>
              ) : (
                <p>• Use Ctrl for most shortcuts on Windows/Linux</p>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="flex justify-end pt-4 border-t">
          <Button onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}

// Hook to manage keyboard shortcuts
export function useKeyboardShortcuts(callbacks: {
  onSave?: () => void
  onUndo?: () => void
  onRedo?: () => void
  onPreview?: () => void
  onDuplicate?: () => void
  onDelete?: () => void
  onMoveUp?: () => void
  onMoveDown?: () => void
  onAddText?: () => void
  onAddImage?: () => void
  onAddHero?: () => void
  onAddCTA?: () => void
  onShowShortcuts?: () => void
}) {
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      const isCtrlOrCmd = e.ctrlKey || e.metaKey

      // Prevent default browser shortcuts
      if (isCtrlOrCmd) {
        switch (e.key) {
          case 's':
            e.preventDefault()
            callbacks.onSave?.()
            break
          case 'z':
            e.preventDefault()
            if (e.shiftKey) {
              callbacks.onRedo?.()
            } else {
              callbacks.onUndo?.()
            }
            break
          case 'p':
            e.preventDefault()
            callbacks.onPreview?.()
            break
          case 'd':
            e.preventDefault()
            callbacks.onDuplicate?.()
            break
          case 'ArrowUp':
            e.preventDefault()
            callbacks.onMoveUp?.()
            break
          case 'ArrowDown':
            e.preventDefault()
            callbacks.onMoveDown?.()
            break
          case '1':
            e.preventDefault()
            callbacks.onAddText?.()
            break
          case '2':
            e.preventDefault()
            callbacks.onAddImage?.()
            break
          case '3':
            e.preventDefault()
            callbacks.onAddHero?.()
            break
          case '4':
            e.preventDefault()
            callbacks.onAddCTA?.()
            break
        }
      } else {
        switch (e.key) {
          case 'Delete':
            e.preventDefault()
            callbacks.onDelete?.()
            break
          case '?':
            e.preventDefault()
            callbacks.onShowShortcuts?.()
            break
        }
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [callbacks])
}
