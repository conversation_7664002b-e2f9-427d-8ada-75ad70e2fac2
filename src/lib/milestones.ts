import milestonesData from '@/data/milestones.json';
import { Milestone } from '@/components/milestones';

/**
 * Get all milestones
 */
export function getAllMilestones(): Milestone[] {
  return milestonesData.milestones;
}

/**
 * Get a specific milestone by index
 */
export function getMilestone(index: number): Milestone | null {
  const milestones = getAllMilestones();
  if (index < 0 || index >= milestones.length) {
    return null;
  }
  return milestones[index];
}

/**
 * Get milestone by ID (1-based)
 */
export function getMilestoneById(id: number): Milestone | null {
  return getMilestone(id - 1);
}

/**
 * Get the total number of milestones
 */
export function getMilestonesCount(): number {
  return getAllMilestones().length;
}

/**
 * Get milestone navigation (previous/next)
 */
export function getMilestoneNavigation(currentIndex: number) {
  const milestones = getAllMilestones();
  const prevIndex = currentIndex - 1;
  const nextIndex = currentIndex + 1;
  
  return {
    previous: prevIndex >= 0 ? {
      index: prevIndex,
      id: prevIndex + 1,
      milestone: milestones[prevIndex]
    } : null,
    next: nextIndex < milestones.length ? {
      index: nextIndex,
      id: nextIndex + 1,
      milestone: milestones[nextIndex]
    } : null
  };
}

/**
 * Get milestones statistics
 */
export function getMilestonesStats() {
  const milestones = getAllMilestones();
  
  const totalDeliverables = milestones.reduce((total, milestone) => {
    return total + (milestone.deliverables?.length || 0);
  }, 0);
  
  const totalTechnologies = milestones.reduce((total, milestone) => {
    return total + (milestone.technologies?.length || 0);
  }, 0);
  
  const uniqueTechnologies = new Set();
  milestones.forEach(milestone => {
    milestone.technologies?.forEach(tech => uniqueTechnologies.add(tech));
  });
  
  return {
    totalMilestones: milestones.length,
    totalDeliverables,
    totalTechnologies,
    uniqueTechnologies: uniqueTechnologies.size,
    milestonesWithDuration: milestones.filter(m => m.duration).length
  };
}

/**
 * Search milestones by keyword
 */
export function searchMilestones(keyword: string): Milestone[] {
  const milestones = getAllMilestones();
  const searchTerm = keyword.toLowerCase();
  
  return milestones.filter(milestone => {
    return (
      milestone.title.toLowerCase().includes(searchTerm) ||
      milestone.description.toLowerCase().includes(searchTerm) ||
      milestone.approach.toLowerCase().includes(searchTerm) ||
      milestone.deliverables?.some(d => d.toLowerCase().includes(searchTerm)) ||
      milestone.technologies?.some(t => t.toLowerCase().includes(searchTerm))
    );
  });
}

/**
 * Get milestones by technology
 */
export function getMilestonesByTechnology(technology: string): Milestone[] {
  const milestones = getAllMilestones();
  const techLower = technology.toLowerCase();
  
  return milestones.filter(milestone => {
    return milestone.technologies?.some(tech => 
      tech.toLowerCase().includes(techLower)
    );
  });
}

/**
 * Get all unique technologies used across milestones
 */
export function getAllTechnologies(): string[] {
  const milestones = getAllMilestones();
  const technologies = new Set<string>();
  
  milestones.forEach(milestone => {
    milestone.technologies?.forEach(tech => technologies.add(tech));
  });
  
  return Array.from(technologies).sort();
}

/**
 * Generate milestone slug from title
 */
export function generateMilestoneSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
}

/**
 * Get milestone summary for cards/previews
 */
export function getMilestoneSummary(milestone: Milestone, maxLength: number = 150): string {
  if (milestone.description.length <= maxLength) {
    return milestone.description;
  }
  
  return milestone.description.substring(0, maxLength).trim() + '...';
}
